package com.youlai.boot.modules.assessment.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.BeanUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youlai.boot.common.enums.ActivityTypeEnum;
import com.youlai.boot.common.enums.AdoptStatusEnum;
import com.youlai.boot.common.enums.AssessmentCategoryEnum;
import com.youlai.boot.common.enums.AssessmentItemTypeEnum;
import com.youlai.boot.common.enums.BizEnvProblemStatusEnum;
import com.youlai.boot.common.enums.InstructionStatusEnum;
import com.youlai.boot.common.enums.MeetingTypeEnum;
import com.youlai.boot.common.enums.OpinionAuditStatusEnum;
import com.youlai.boot.common.enums.ScoringCategoryEnum;
import com.youlai.boot.common.enums.WorkTypeEnum;
import com.youlai.boot.core.security.util.SecurityUtils;
import com.youlai.boot.modules.activity.mapper.ActivityMapper;
import com.youlai.boot.modules.activity.model.entity.Activity;
import com.youlai.boot.modules.assessment.converter.AssessmentScoreDetailsConverter;
import com.youlai.boot.modules.assessment.mapper.AssessmentScoreDetailsMapper;
import com.youlai.boot.modules.assessment.mapper.AssessmentScoreMapper;
import com.youlai.boot.modules.assessment.model.entity.AssessmentScoreDetails;
import com.youlai.boot.modules.assessment.model.bo.AssessmentRuleCacheBO;
import com.youlai.boot.modules.assessment.model.entity.AssessmentRules;
import com.youlai.boot.modules.assessment.model.entity.AssessmentScore;
import com.youlai.boot.modules.assessment.model.form.AssessmentScoreDetailsForm;
import com.youlai.boot.modules.assessment.model.query.AssessmentScoreDetailDataQuery;
import com.youlai.boot.modules.assessment.model.query.AssessmentScoreDetailsQuery;
import com.youlai.boot.modules.assessment.model.vo.AssessmentScoreDetailDataVO;
import com.youlai.boot.modules.assessment.model.vo.AssessmentScoreDetailsVO;
import com.youlai.boot.modules.assessment.service.AssessmentScoreDetailsService;
import com.youlai.boot.modules.assessment.service.AssessmentScoreService;
import com.youlai.boot.modules.assessment.utils.AssessmentRulesCacheUtils;
import com.youlai.boot.modules.meeting.mapper.MeetingMapper;
import com.youlai.boot.modules.meeting.model.entity.Meeting;
import com.youlai.boot.modules.opinion.mapper.OpinionMapper;
import com.youlai.boot.modules.opinion.model.entity.Opinion;
import com.youlai.boot.modules.problem.mapper.ProblemMapper;
import com.youlai.boot.modules.problem.model.entity.Problem;
import com.youlai.boot.modules.work.mapper.KeyWorkMapper;
import com.youlai.boot.modules.work.model.entity.KeyWork;
import com.youlai.boot.modules.assessment.service.AssessmentRulesService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 履职考核得分细则表服务实现类
 *
 */
@Service
@RequiredArgsConstructor
public class AssessmentScoreDetailsServiceImpl extends ServiceImpl<AssessmentScoreDetailsMapper, AssessmentScoreDetails>
        implements AssessmentScoreDetailsService {

    private final AssessmentScoreDetailsConverter assessmentScoreDetailsConverter;
    private final AssessmentScoreService assessmentScoreService;
    private final AssessmentRulesService assessmentRulesService;
    private final AssessmentScoreMapper assessmentScoreMapper;
    private final OpinionMapper opinionMapper;
    private final ActivityMapper activityMapper;
    private final MeetingMapper meetingMapper;
    private final KeyWorkMapper keyWorkMapper;
    private final ProblemMapper problemMapper;
    private final AssessmentRulesCacheUtils assessmentRulesCacheUtils;

    /**
     * 履职考核得分细则表分页列表
     *
     * @param queryParams 查询参数
     * @return 分页数据
     */
    @Override
    public IPage<AssessmentScoreDetailsVO> getAssessmentScoreDetailsPage(AssessmentScoreDetailsQuery queryParams) {
        // 参数构建
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<AssessmentScoreDetailsVO> page = new Page<>(pageNum, pageSize);

        // 查询数据
        Page<AssessmentScoreDetailsVO> result = this.baseMapper.getAssessmentScoreDetailsPage(page, queryParams);
        return result;
    }

    /**
     * 获取用户的得分细则详情
     *
     * @param memberId 用户ID
     * @param year     年度
     * @return 得分细则详情列表
     */
    @Override
    public List<AssessmentScoreDetailsVO> getMemberScoreDetails(Long memberId, Integer year) {
        return this.baseMapper.getMemberScoreDetails(memberId, year);
    }

    /**
     * 获取履职考核得分细则表表单数据
     *
     * @param id 得分细则ID
     * @return 表单数据
     */
    @Override
    public AssessmentScoreDetailsForm getAssessmentScoreDetailsFormData(Long id) {
        AssessmentScoreDetails entity = this.getById(id);
        return assessmentScoreDetailsConverter.toForm(entity);
    }

    /**
     * 新增履职考核得分细则表
     *
     * @param formData 得分细则表单对象
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean saveAssessmentScoreDetails(AssessmentScoreDetailsForm formData) {
        // 表单数据转换为实体
        AssessmentScoreDetails entity = assessmentScoreDetailsConverter.toEntity(formData);

        // 设置创建人
        entity.setCreateBy(SecurityUtils.getUserId());

        // 保存实体
        boolean result = this.save(entity);

        if (result) {
            // 同步更新得分总表
            syncScoreToTotal(entity, true);
        }

        return result;
    }

    /**
     * 修改履职考核得分细则表
     *
     * @param id       得分细则ID
     * @param formData 得分细则表单对象
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean updateAssessmentScoreDetails(Long id, AssessmentScoreDetailsForm formData) {
        // 获取原实体
        AssessmentScoreDetails existingEntity = this.getById(id);
        Assert.notNull(existingEntity, "履职考核得分细则表不存在");

        // 表单数据转换为实体
        AssessmentScoreDetails entity = assessmentScoreDetailsConverter.toEntity(formData);
        entity.setId(id);

        // 设置更新人
        entity.setUpdateBy(SecurityUtils.getUserId());

        // 更新实体
        boolean result = this.updateById(entity);

        if (result) {
            // 先减去原来的得分，再加上新的得分
            syncScoreToTotal(existingEntity, false);
            syncScoreToTotal(entity, true);
        }

        return result;
    }

    /**
     * 删除履职考核得分细则表
     *
     * @param ids 得分细则ID，多个以英文逗号(,)分割
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean deleteAssessmentScoreDetails(String ids) {
        Assert.notBlank(ids, "删除的履职考核得分细则表ID不能为空");

        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());

        // 获取要删除的实体列表，用于同步更新得分总表
        List<AssessmentScoreDetails> entitiesToDelete = this.listByIds(idList);

        // 逻辑删除
        boolean result = this.removeByIds(idList);

        if (result) {
            // 同步更新得分总表（减去删除的得分）
            for (AssessmentScoreDetails entity : entitiesToDelete) {
                syncScoreToTotal(entity, false);
            }
        }

        return result;
    }

    /**
     * 通过dataId列表和得分类型删除履职得分细则数据
     * 
     * @param dataIds 数据id列表
     * @param type    得分类型
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean deleteByDataIds(List<Long> dataIds, AssessmentItemTypeEnum type) {
        LambdaQueryWrapper<AssessmentScoreDetails> queryWrapper = new LambdaQueryWrapper<AssessmentScoreDetails>()
                .in(AssessmentScoreDetails::getDataId, dataIds)
                .eq(AssessmentScoreDetails::getType, type);
        // 获取所有需要删除的得分细则列表
        List<AssessmentScoreDetails> asdList = this.list(queryWrapper);

        // 删除得分细则并同步总得分表数据
        if (!asdList.isEmpty()) {
            // 同步更新得分总表（减去删除的得分）
            for (AssessmentScoreDetails entity : asdList) {
                syncScoreToTotal(entity, false);
            }
        }

        return this.remove(queryWrapper);
    }

    /**
     * 同步得分到总表
     *
     * @param entity 得分细则实体
     * @param isAdd  是否是加分
     */
    @Transactional
    private void syncScoreToTotal(AssessmentScoreDetails entity, Boolean isAdd) {
        // 获取考核规则
        AssessmentRules rule = assessmentRulesService.getById(entity.getRuleId());
        if (rule == null) {
            throw new RuntimeException("考核规则不存在");
        }

        // 如果是减分需要将rule的分数取反
        int score = isAdd ? rule.getScore() : -rule.getScore();

        // 获取一下年份数据
        int year = LocalDateTime.now().getYear();
        // 创建总得分对象
        AssessmentScore assessmentScore = new AssessmentScore();
        // 初始分数全是0
        assessmentScore.setActivityScore(0);
        assessmentScore.setMeetingScore(0);
        assessmentScore.setKeyWorkScore(0);
        assessmentScore.setEnvironmentScore(0);
        assessmentScore.setOpinionScore(0);

        // 通过 : 截取 rule.getRuleCode() 获取第一个字符串
        AssessmentItemTypeEnum assessmentType = AssessmentItemTypeEnum.getEnumByValue(rule.getRuleCode().split(":")[0]);
        // 根据不同的类型获取不同的年份
        switch (assessmentType) {
            case OPINION:
                Opinion opinion = opinionMapper.selectById(entity.getDataId());
                if (opinion.getCreateTime() != null) {
                    year = opinion.getCreateTime().getYear();
                }
                assessmentScore.setOpinionScore(score);
                break;
            case ENVIRONMENT:
                Problem problem = problemMapper.selectById(entity.getDataId());
                if (problem.getCreateTime() != null) {
                    year = problem.getCreateTime().getYear();
                }
                assessmentScore.setEnvironmentScore(score);
                break;
            case KEY_WORK:
                KeyWork keyWork = keyWorkMapper.selectById(entity.getDataId());
                if (keyWork.getStartTime() != null) {
                    year = keyWork.getStartTime().getYear();
                }
                assessmentScore.setKeyWorkScore(score);
                break;
            case ACTIVITY, CHARITY:
                Activity activity = activityMapper.selectById(entity.getDataId());
                if (activity.getStartTime() != null) {
                    year = activity.getStartTime().getYear();
                }
                assessmentScore.setActivityScore(score);
                break;
            case MEETING:
                Meeting meeting = meetingMapper.selectById(entity.getDataId());
                if (meeting.getStartTime() != null) {
                    year = meeting.getStartTime().getYear();
                }
                assessmentScore.setMeetingScore(score);
                break;

            default:
                break;
        }

        assessmentScore.setYear(year);
        assessmentScore.setCategory(entity.getCategory());
        assessmentScore.setMemberId(entity.getMemberId());

        // 查看一下是否已存在对应的记录
        LambdaQueryWrapper<AssessmentScore> queryWrapper = new LambdaQueryWrapper<AssessmentScore>()
                .eq(AssessmentScore::getCategory, entity.getCategory())
                .eq(AssessmentScore::getMemberId, entity.getMemberId())
                .eq(AssessmentScore::getYear, year);
        AssessmentScore existingScore = assessmentScoreMapper.selectOne(queryWrapper);
        if (existingScore == null) {
            assessmentScore.setCreateBy(SecurityUtils.getUserId());
            assessmentScore.setCreateTime(LocalDateTime.now());
            // assessmentScore.setIsDeleted(0);
            assessmentScoreService.save(assessmentScore);
        } else {
            assessmentScore.setId(existingScore.getId());
            assessmentScore.setUpdateBy(SecurityUtils.getUserId());
            assessmentScore.setUpdateTime(LocalDateTime.now());
            // 对应分数加一下
            assessmentScore.setActivityScore(existingScore.getActivityScore() + assessmentScore.getActivityScore());
            assessmentScore.setMeetingScore(existingScore.getMeetingScore() + assessmentScore.getMeetingScore());
            assessmentScore.setKeyWorkScore(existingScore.getKeyWorkScore() + assessmentScore.getKeyWorkScore());
            assessmentScore
                    .setEnvironmentScore(existingScore.getEnvironmentScore() + assessmentScore.getEnvironmentScore());
            assessmentScore.setOpinionScore(existingScore.getOpinionScore() + assessmentScore.getOpinionScore());
            assessmentScoreService.updateById(assessmentScore);
        }
    }

    @Override
    public IPage<AssessmentScoreDetailDataVO> getAssessmentScoreDetailDataPage(
            AssessmentScoreDetailDataQuery queryParams) {
        int pageNum = queryParams.getPageNum();
        int pageSize = queryParams.getPageSize();
        Page<AssessmentScoreDetailDataVO> page = new Page<>(pageNum, pageSize);
        if (queryParams.getDataType() == AssessmentItemTypeEnum.MEETING) {
            // 连接会议表查询数据
            Page<AssessmentScoreDetailDataVO> dataList = this.baseMapper.getAssessmentScoreDetailMeetingData(page,
                    queryParams);
            // 翻译一下对应的dataType
            dataList.getRecords().forEach(item -> {
                item.setDataTypeLabel(MeetingTypeEnum.getEnumByValue(item.getDataType()).getLabel());
            });
            return dataList;
        } else if (queryParams.getDataType() == AssessmentItemTypeEnum.ACTIVITY) {
            // 连接活动表查询数据
            Page<AssessmentScoreDetailDataVO> dataList = this.baseMapper.getAssessmentScoreDetailAllActivityData(page,
                    queryParams);
            // Page<AssessmentScoreDetailDataVO> dataList =
            // this.baseMapper.getAssessmentScoreDetailActivityData(page,
            // queryParams);
            // 翻译一下对应的dataType
            dataList.getRecords().forEach(item -> {
                item.setDataTypeLabel(ActivityTypeEnum.getEnumByValue(item.getDataType()).getLabel());
            });
            return dataList;
        } else if (queryParams.getDataType() == AssessmentItemTypeEnum.OPINION) {
            // 连接意见表查询数据
            Page<AssessmentScoreDetailDataVO> dataList = this.baseMapper.getAssessmentScoreDetailOpinionData(page,
                    queryParams);
            return dataList;
        } else if (queryParams.getDataType() == AssessmentItemTypeEnum.KEY_WORK) {
            // 连接重点工作表查询数据
            Page<AssessmentScoreDetailDataVO> dataList = this.baseMapper.getAssessmentScoreDetailKeyWorkData(page,
                    queryParams);
            // 翻译一下对应的dataType
            dataList.getRecords().forEach(item -> {
                item.setDataTypeLabel(WorkTypeEnum.getEnumByValue(item.getDataType()).getLabel());
            });
            return dataList;
        } else if (queryParams.getDataType() == AssessmentItemTypeEnum.ENVIRONMENT) {
            // 连接营商环境问题表查询数据
            Page<AssessmentScoreDetailDataVO> dataList = this.baseMapper.getAssessmentScoreDetailEnvironmentData(page,
                    queryParams);
            // 翻译一下对应的dataType
            dataList.getRecords().forEach(item -> {
                item.setDataTypeLabel(item.getDataType());
            });
            return dataList;
        } else if (queryParams.getDataType() == AssessmentItemTypeEnum.CHARITY) {
            // 连接活动表并限定类型查询数据
            Page<AssessmentScoreDetailDataVO> dataList = this.baseMapper.getAssessmentScoreDetailCharityData(page,
                    queryParams);
            // 翻译一下对应的dataType
            dataList.getRecords().forEach(item -> {
                item.setDataTypeLabel(ActivityTypeEnum.getEnumByValue(item.getDataType()).getLabel());
            });
            return dataList;
        } else {
            // 不匹配直接报错
            throw new RuntimeException("不支持的得分类型");
        }
    }

    /**
     * 重新统计并同步所有用户的履职考核得分
     *
     * @describtion 后续可以加一个年度限定（仅更新某一年度的数据）、也可以再加一个用户限定（仅更新某个用户的数据）
     */
    @Override
    @Transactional
    public boolean reCalculateAllScore() {
        // 先清空所有得分总表数据，避免唯一键冲突
        assessmentScoreService.remove(new LambdaQueryWrapper<AssessmentScore>());

        // 统一收集所有模块的得分数据，避免重复插入
        Map<String, AssessmentScore> allScoreMap = new HashMap<>();

        // 重新统计意见征集汇总得分
        Map<String, AssessmentScore> opinionScoreMap = reCalculateOpinionScoreInternal(null);
        mergeScoreMap(allScoreMap, opinionScoreMap);

        // 重新统计营商环境问题反馈得分
        Map<String, AssessmentScore> bizEnvScoreMap = reCalculateBizEnvProblemScoreInternal(null);
        mergeScoreMap(allScoreMap, bizEnvScoreMap);

        // 重新统计参加会议得分
        Map<String, AssessmentScore> meetingScoreMap = reCalculateMeetingScoreInternal(null);
        mergeScoreMap(allScoreMap, meetingScoreMap);

        // 重新统计年度重点工作得分
        Map<String, AssessmentScore> keyWorkScoreMap = reCalculateKeyWorkScoreInternal(null);
        mergeScoreMap(allScoreMap, keyWorkScoreMap);

        // 重新统计参加活动得分
        Map<String, AssessmentScore> activityScoreMap = reCalculateActivityScoreInternal(null);
        mergeScoreMap(allScoreMap, activityScoreMap);

        // 重新统计办好事、解难题、做公益情况得分
        Map<String, AssessmentScore> charityScoreMap = reCalculateCharityScoreInternal(null);
        mergeScoreMap(allScoreMap, charityScoreMap);

        // 统一保存所有得分总表数据
        if (!allScoreMap.isEmpty()) {
            List<AssessmentScore> allScoreList = new ArrayList<>(allScoreMap.values());
            return assessmentScoreService.saveBatch(allScoreList);
        }

        return true;
    }

    /**
     * 合并得分数据到总的得分Map中
     *
     * @param allScoreMap    总的得分Map
     * @param moduleScoreMap 模块得分Map
     */
    private void mergeScoreMap(Map<String, AssessmentScore> allScoreMap, Map<String, AssessmentScore> moduleScoreMap) {
        moduleScoreMap.forEach((key, moduleScore) -> {
            // 如果对应分数不存在则默认为0
            if (moduleScore.getActivityScore() == null) {
                moduleScore.setActivityScore(0);
            }
            if (moduleScore.getMeetingScore() == null) {
                moduleScore.setMeetingScore(0);
            }
            if (moduleScore.getKeyWorkScore() == null) {
                moduleScore.setKeyWorkScore(0);
            }
            if (moduleScore.getEnvironmentScore() == null) {
                moduleScore.setEnvironmentScore(0);
            }
            if (moduleScore.getOpinionScore() == null) {
                moduleScore.setOpinionScore(0);
            }

            AssessmentScore existingScore = allScoreMap.get(key);
            if (existingScore == null) {
                // 如果不存在，直接添加
                allScoreMap.put(key, moduleScore);
            } else {
                // 如果存在，合并各项得分
                existingScore.setActivityScore(existingScore.getActivityScore() + moduleScore.getActivityScore());
                existingScore.setMeetingScore(existingScore.getMeetingScore() + moduleScore.getMeetingScore());
                existingScore.setKeyWorkScore(existingScore.getKeyWorkScore() + moduleScore.getKeyWorkScore());
                existingScore
                        .setEnvironmentScore(existingScore.getEnvironmentScore() + moduleScore.getEnvironmentScore());
                existingScore.setOpinionScore(existingScore.getOpinionScore() + moduleScore.getOpinionScore());
            }
        });
    }

    /**
     * 重新统计意见征集汇总得分（内部方法，返回得分Map）
     *
     * @param memberId 用户ID，为null时统计所有用户
     * @return 得分Map，key为"category:memberId:year"
     */
    private Map<String, AssessmentScore> reCalculateOpinionScoreInternal(Long memberId) {
        // 获取所有未删除的意见征集数据
        List<Opinion> opinions = opinionMapper.selectList(new LambdaQueryWrapper<Opinion>()
                .eq(Opinion::getIsDeleted, 0)
                .isNotNull(Opinion::getCategory)
                .ne(Opinion::getCategory, "")
                .eq(memberId != null, Opinion::getMemberId, memberId));
        if (opinions.isEmpty()) {
            return new HashMap<>();
        }
        // 删除所有的意见征集得分细则
        this.remove(new LambdaQueryWrapper<AssessmentScoreDetails>()
                .eq(AssessmentScoreDetails::getType, AssessmentItemTypeEnum.OPINION)
                .eq(memberId != null, AssessmentScoreDetails::getMemberId, memberId));

        // 需要更新插入的数据
        List<AssessmentScoreDetails> assessmentScoreDetailsList = new ArrayList<>();
        // 需要更新的得分总表的数据的Map
        Map<String, AssessmentScore> assessmentScoreMap = new HashMap<>();
        // 获取当前登录用户ID
        Long createBy = SecurityUtils.getUserId() == null ? 0L : SecurityUtils.getUserId();
        for (Opinion opinion : opinions) {
            // 获取对应的计分规则
            AssessmentCategoryEnum category = ScoringCategoryEnum.toAssessmentCategoryEnum(opinion.getCategory());
            AssessmentRuleCacheBO submitRule = assessmentRulesCacheUtils.getAssessmentRuleCache(category,
                    AssessmentItemTypeEnum.OPINION.getValue() + ":" + OpinionAuditStatusEnum.SUBMITTED.getValue());
            // 即将插入的新数据对象
            AssessmentScoreDetails assessmentScoreDetails = new AssessmentScoreDetails();
            assessmentScoreDetails.setCategory(category);
            assessmentScoreDetails.setType(AssessmentItemTypeEnum.OPINION);
            assessmentScoreDetails.setMemberId(opinion.getMemberId());
            assessmentScoreDetails.setDataId(opinion.getId());
            if (createBy != null) {
                assessmentScoreDetails.setCreateBy(createBy);
            }
            assessmentScoreDetails.setRemark("意见征集重新统计分数");

            // 统计意见建议的总得分
            AssessmentScore assessmentScore = new AssessmentScore();
            String mapKey = category.getValue() + ":" + opinion.getMemberId() + ":" + opinion.getCreateTime().getYear();
            assessmentScore.setCategory(category);
            assessmentScore.setMemberId(opinion.getMemberId());
            assessmentScore.setYear(opinion.getCreateTime().getYear());
            Integer opinionScore = 0;
            if (assessmentScoreMap.get(mapKey) != null) {
                assessmentScore = assessmentScoreMap.get(mapKey);
                opinionScore = assessmentScore.getOpinionScore();
            }
            // 是否需要添加进待更新列表
            boolean needUpdate = false;
            // 如果对应的规则存在则添加进待插入列表
            if (submitRule != null) {
                // 先统计提交的得分
                assessmentScoreDetails.setRuleId(submitRule.getId());
                if (createBy != null) {
                    assessmentScoreDetails.setCreateBy(createBy);
                }
                opinionScore += submitRule.getScore();
                needUpdate = true;
                assessmentScoreDetailsList.add(assessmentScoreDetails);
            }

            // 统计被采纳的得分
            if (opinion.getStatus() == OpinionAuditStatusEnum.ADOPTED) {
                AssessmentRuleCacheBO adoptRule = assessmentRulesCacheUtils.getAssessmentRuleCache(category,
                        AssessmentItemTypeEnum.OPINION.getValue() + ":" + OpinionAuditStatusEnum.ADOPTED.getValue());
                if (adoptRule != null) {
                    // 复制assessmentScoreDetails属性生成新的插入对象
                    AssessmentScoreDetails adoptScoreDetails = new AssessmentScoreDetails();
                    BeanUtils.copyProperties(assessmentScoreDetails, adoptScoreDetails);
                    adoptScoreDetails.setRuleId(adoptRule.getId());
                    opinionScore += adoptRule.getScore();
                    needUpdate = true;
                    assessmentScoreDetailsList.add(adoptScoreDetails);
                }
            }
            if (needUpdate) {
                assessmentScore.setOpinionScore(opinionScore);
                assessmentScoreMap.put(mapKey, assessmentScore);
            }
        }
        // 保存得分细则数据
        this.saveBatch(assessmentScoreDetailsList);

        // 返回得分Map供上层方法统一处理
        return assessmentScoreMap;
    }

    /**
     * 重新统计营商环境问题反馈得分（内部方法，返回得分Map）
     *
     * @param memberId 用户ID，为null时统计所有用户
     * @return 得分Map，key为"category:memberId:year"
     */
    private Map<String, AssessmentScore> reCalculateBizEnvProblemScoreInternal(Long memberId) {
        // 获取所有未删除的营商环境问题反馈数据
        List<Problem> problems = problemMapper.selectList(new LambdaQueryWrapper<Problem>()
                .eq(Problem::getIsDeleted, 0)
                .isNotNull(Problem::getCategory)
                .ne(Problem::getCategory, "")
                .eq(memberId != null, Problem::getMemberId, memberId));
        if (problems.isEmpty()) {
            return new HashMap<>();
        }
        // 删除所有的营商环境问题反馈得分细则
        this.remove(new LambdaQueryWrapper<AssessmentScoreDetails>()
                .eq(AssessmentScoreDetails::getType, AssessmentItemTypeEnum.ENVIRONMENT)
                .eq(memberId != null, AssessmentScoreDetails::getMemberId, memberId));

        // 需要更新插入的数据
        List<AssessmentScoreDetails> assessmentScoreDetailsList = new ArrayList<>();
        // 需要更新的得分总表的数据的Map
        Map<String, AssessmentScore> assessmentScoreMap = new HashMap<>();
        // 获取当前登录用户ID
        Long createBy = SecurityUtils.getUserId() == null ? 0L : SecurityUtils.getUserId();
        // 遍历所有的营商环境问题反馈数据
        for (Problem problem : problems) {
            // 获取对应的计分规则
            AssessmentCategoryEnum category = ScoringCategoryEnum.toAssessmentCategoryEnum(problem.getCategory());
            AssessmentRuleCacheBO submitRule = assessmentRulesCacheUtils.getAssessmentRuleCache(category,
                    AssessmentItemTypeEnum.ENVIRONMENT.getValue() + ":" + BizEnvProblemStatusEnum.SUBMITTED.getValue());
            // 即将插入的新数据对象
            AssessmentScoreDetails assessmentScoreDetails = new AssessmentScoreDetails();
            assessmentScoreDetails.setCategory(category);
            assessmentScoreDetails.setType(AssessmentItemTypeEnum.ENVIRONMENT);
            assessmentScoreDetails.setMemberId(problem.getMemberId());
            assessmentScoreDetails.setDataId(problem.getId());
            assessmentScoreDetails.setCreateBy(createBy);
            assessmentScoreDetails.setRemark("营商环境问题反馈重新统计分数");

            // 统计营商环境问题反馈的总得分
            AssessmentScore assessmentScore = new AssessmentScore();
            String mapKey = category.getValue() + ":" + problem.getMemberId() + ":" + problem.getSubmitTime().getYear();
            assessmentScore.setCategory(category);
            assessmentScore.setMemberId(problem.getMemberId());
            assessmentScore.setYear(problem.getSubmitTime().getYear());
            Integer eviromentScore = 0;
            if (assessmentScoreMap.get(mapKey) != null) {
                assessmentScore = assessmentScoreMap.get(mapKey);
                eviromentScore = assessmentScore.getEnvironmentScore();
            }
            // 是否需要添加进待更新列表
            boolean needUpdate = false;

            // 如果对应的规则存在则添加进待插入列表
            if (submitRule != null) {
                assessmentScoreDetails.setRuleId(submitRule.getId());
                eviromentScore += submitRule.getScore();
                needUpdate = true;
                assessmentScoreDetailsList.add(assessmentScoreDetails);
            }
            // 处理被采纳的反馈
            if (problem.getAdoptStatus() == AdoptStatusEnum.PASS) {
                AssessmentRuleCacheBO adoptRule = assessmentRulesCacheUtils.getAssessmentRuleCache(category,
                        AssessmentItemTypeEnum.ENVIRONMENT.getValue() + ":"
                                + BizEnvProblemStatusEnum.ADOPTED.getValue());
                if (adoptRule != null) {
                    // 复制assessmentScoreDetails属性生成新的插入对象
                    AssessmentScoreDetails adoptScoreDetails = new AssessmentScoreDetails();
                    BeanUtils.copyProperties(assessmentScoreDetails, adoptScoreDetails);
                    adoptScoreDetails.setRuleId(adoptRule.getId());
                    eviromentScore += adoptRule.getScore();
                    needUpdate = true;
                    assessmentScoreDetailsList.add(adoptScoreDetails);
                }
            }
            // 处理被领导批示的反馈
            if (problem.getInstructionStatus() == InstructionStatusEnum.PASS
                    && StringUtils.isBlank(problem.getLeaderInstruction())) {
                // HACK: 因为数据库中存储的领导批示是Q和S，所以这里需要特殊处理一下;使用逗号分隔是考虑到未来可能会有多个领导批示
                List<String> leaderInstructions = Arrays.asList(problem.getLeaderInstruction().trim().split(","));
                if (leaderInstructions.contains("Q")) {
                    // 区领导批示
                    AssessmentRuleCacheBO instructRule = assessmentRulesCacheUtils.getAssessmentRuleCache(category,
                            AssessmentItemTypeEnum.ENVIRONMENT.getValue() + ":"
                                    + BizEnvProblemStatusEnum.INSTRUCTED_DISTRICT.getValue());
                    if (instructRule != null) {
                        // 复制assessmentScoreDetails属性生成新的插入对象
                        AssessmentScoreDetails instructScoreDetails = new AssessmentScoreDetails();
                        BeanUtils.copyProperties(assessmentScoreDetails, instructScoreDetails);
                        instructScoreDetails.setRuleId(instructRule.getId());
                        eviromentScore += instructRule.getScore();
                        needUpdate = true;
                        assessmentScoreDetailsList.add(instructScoreDetails);
                    }
                }
                if (leaderInstructions.contains("S")) {
                    // 市领导批示
                    AssessmentRuleCacheBO instructRule = assessmentRulesCacheUtils.getAssessmentRuleCache(category,
                            AssessmentItemTypeEnum.ENVIRONMENT.getValue() + ":"
                                    + BizEnvProblemStatusEnum.INSTRUCTED_CITY.getValue());
                    if (instructRule != null) {
                        // 复制assessmentScoreDetails属性生成新的插入对象
                        AssessmentScoreDetails instructScoreDetails = new AssessmentScoreDetails();
                        BeanUtils.copyProperties(assessmentScoreDetails, instructScoreDetails);
                        instructScoreDetails.setRuleId(instructRule.getId());
                        eviromentScore += instructRule.getScore();
                        needUpdate = true;
                        assessmentScoreDetailsList.add(instructScoreDetails);
                    }
                }
            }
            if (needUpdate) {
                assessmentScore.setEnvironmentScore(eviromentScore);
                assessmentScoreMap.put(mapKey, assessmentScore);
            }
        }
        // 保存得分细则数据
        this.saveBatch(assessmentScoreDetailsList);

        // 返回得分Map供上层方法统一处理
        return assessmentScoreMap;
    }

    /**
     * 重新统计参加会议得分（内部方法，返回得分Map）
     *
     * @param memberId 用户ID，为null时统计所有用户
     * @return 得分Map，key为"category:memberId:year"
     */
    private Map<String, AssessmentScore> reCalculateMeetingScoreInternal(Long memberId) {
        // 获取所有未删除的会议数据
        List<Meeting> meetings = meetingMapper.selectList(new LambdaQueryWrapper<Meeting>()
                .eq(Meeting::getIsDeleted, 0)
                .isNotNull(Meeting::getCategory)
                .ne(Meeting::getCategory, "")
                .like(memberId != null, Meeting::getParticipants, "%{\"id\":" + memberId +
                        "}%"));
        if (meetings.isEmpty()) {
            return new HashMap<>();
        }
        // 删除所有的参加会议得分细则
        this.remove(new LambdaQueryWrapper<AssessmentScoreDetails>()
                .eq(AssessmentScoreDetails::getType, AssessmentItemTypeEnum.MEETING)
                .eq(memberId != null, AssessmentScoreDetails::getMemberId, memberId));

        // 需要更新插入的数据
        List<AssessmentScoreDetails> assessmentScoreDetailsList = new ArrayList<>();
        // 需要更新的总得分的数据的map
        Map<String, AssessmentScore> assessmentScoreMap = new HashMap<>();
        // 获取当前登录用户ID
        Long createBy = SecurityUtils.getUserId() == null ? 0L : SecurityUtils.getUserId();
        for (Meeting meeting : meetings) {
            // 获取对应的会议所属部门列表
            List<ScoringCategoryEnum> categoryList = Arrays.stream(meeting.getCategory().split(","))
                    .map(ScoringCategoryEnum::getEnumByValue).collect(Collectors.toList());
            // 获取会议参与人员id列表
            List<Long> participantIds;
            if (memberId == null) {
                participantIds = JSONUtil.toList(meeting.getParticipants(), Map.class)
                        .stream()
                        .map(map -> Long.valueOf(map.get("id").toString()))
                        .collect(Collectors.toList());
            } else {
                participantIds = Arrays.asList(memberId);
            }
            // 遍历参与人员与会议所属部门添加需要更新的细则对象
            participantIds.forEach(participantId -> {
                categoryList.forEach(originalCategory -> {
                    // 转换一下对应的枚举
                    AssessmentCategoryEnum category = ScoringCategoryEnum.toAssessmentCategoryEnum(originalCategory);
                    // 获取对应的计分规则
                    AssessmentRuleCacheBO rule = assessmentRulesCacheUtils.getAssessmentRuleCache(category,
                            AssessmentItemTypeEnum.MEETING.getValue() + ":" + meeting.getMeetingType().getValue());
                    // 如果对应的规则存在则添加进待插入列表
                    if (rule != null) {
                        // 即将插入的新数据对象
                        AssessmentScoreDetails assessmentScoreDetails = new AssessmentScoreDetails();
                        assessmentScoreDetails.setCategory(category);
                        assessmentScoreDetails.setType(AssessmentItemTypeEnum.MEETING);
                        assessmentScoreDetails.setMemberId(participantId);
                        assessmentScoreDetails.setDataId(meeting.getId());
                        assessmentScoreDetails.setCreateBy(createBy);
                        assessmentScoreDetails.setRemark("参加会议重新统计分数");
                        assessmentScoreDetails.setRuleId(rule.getId());
                        assessmentScoreDetailsList.add(assessmentScoreDetails);

                        // 统计会议相关的总得分
                        AssessmentScore assessmentScore = new AssessmentScore();
                        String mapKey = category.getValue() + ":" + participantId + ":"
                                + meeting.getStartTime().getYear();
                        assessmentScore.setCategory(category);
                        assessmentScore.setMemberId(participantId);
                        assessmentScore.setYear(meeting.getStartTime().getYear());
                        Integer meetingScore = rule.getScore();
                        if (assessmentScoreMap.get(mapKey) != null) {
                            assessmentScore = assessmentScoreMap.get(mapKey);
                            meetingScore += assessmentScore.getMeetingScore();
                        }
                        assessmentScore.setMeetingScore(meetingScore);
                        assessmentScoreMap.put(mapKey, assessmentScore);
                    }
                });
            });
        }
        // 保存得分细则数据
        this.saveBatch(assessmentScoreDetailsList);

        // 返回得分Map供上层方法统一处理
        return assessmentScoreMap;
    }

    /**
     * 重新统计年度重点工作得分（内部方法，返回得分Map）
     *
     * @param memberId 用户ID，为null时统计所有用户
     * @return 得分Map，key为"category:memberId:year"
     */
    private Map<String, AssessmentScore> reCalculateKeyWorkScoreInternal(Long memberId) {
        // 获取所有未删除的年度重点工作数据
        List<KeyWork> keyWorks = keyWorkMapper.selectList(new LambdaQueryWrapper<KeyWork>()
                .eq(KeyWork::getIsDeleted, 0)
                .isNotNull(KeyWork::getCategory)
                .ne(KeyWork::getCategory, "")
                .like(memberId != null, KeyWork::getParticipants, "%{\"id\":" + memberId +
                        "}%"));
        if (keyWorks.isEmpty()) {
            return new HashMap<>();
        }
        // 删除所有的年度重点工作得分细则
        this.remove(new LambdaQueryWrapper<AssessmentScoreDetails>()
                .eq(AssessmentScoreDetails::getType, AssessmentItemTypeEnum.KEY_WORK)
                .eq(memberId != null, AssessmentScoreDetails::getMemberId, memberId));

        // 需要更新的得分总表的数据的Map
        Map<String, AssessmentScore> assessmentScoreMap = new HashMap<>();
        // 需要更新插入的数据
        List<AssessmentScoreDetails> assessmentScoreDetailsList = new ArrayList<>();
        // 获取当前登录用户ID
        Long createBy = SecurityUtils.getUserId() == null ? 0L : SecurityUtils.getUserId();
        for (KeyWork keyWork : keyWorks) {
            // 获取对应的重点工作分类列表
            List<ScoringCategoryEnum> categoryList = Arrays.stream(keyWork.getCategory().split(","))
                    .map(ScoringCategoryEnum::getEnumByValue).collect(Collectors.toList());
            // 获取对应的重点工作参与人员id列表
            List<Long> participantIds;
            if (memberId == null) {
                participantIds = JSONUtil.toList(keyWork.getParticipants(), Map.class)
                        .stream()
                        .map(map -> Long.valueOf(map.get("id").toString()))
                        .collect(Collectors.toList());
            } else {
                participantIds = Arrays.asList(memberId);
            }
            // 遍历参与人员与重点工作分类添加需要更新的细则对象
            participantIds.forEach(participantId -> {
                categoryList.forEach(originalCategory -> {
                    // 转换一下对应的枚举
                    AssessmentCategoryEnum category = ScoringCategoryEnum.toAssessmentCategoryEnum(originalCategory);
                    // 获取对应的计分规则
                    AssessmentRuleCacheBO rule = assessmentRulesCacheUtils.getAssessmentRuleCache(category,
                            AssessmentItemTypeEnum.KEY_WORK.getValue() + ":" + keyWork.getWorkType().getValue());
                    // 如果对应的规则存在则添加进待插入列表
                    if (rule != null) {
                        // 即将插入的新数据对象
                        AssessmentScoreDetails assessmentScoreDetails = new AssessmentScoreDetails();
                        assessmentScoreDetails.setCategory(category);
                        assessmentScoreDetails.setType(AssessmentItemTypeEnum.KEY_WORK);
                        assessmentScoreDetails.setMemberId(participantId);
                        assessmentScoreDetails.setDataId(keyWork.getId());
                        assessmentScoreDetails.setCreateBy(createBy);
                        assessmentScoreDetails.setRemark("年度重点工作重新统计分数");
                        assessmentScoreDetails.setRuleId(rule.getId());
                        assessmentScoreDetailsList.add(assessmentScoreDetails);

                        // 统计年度重点工作相关的总得分
                        AssessmentScore assessmentScore = new AssessmentScore();
                        String mapKey = category.getValue() + ":" + participantId + ":"
                                + keyWork.getStartTime().getYear();
                        assessmentScore.setCategory(category);
                        assessmentScore.setMemberId(participantId);
                        assessmentScore.setYear(keyWork.getStartTime().getYear());
                        Integer keyWorkScore = rule.getScore();
                        if (assessmentScoreMap.get(mapKey) != null) {
                            assessmentScore = assessmentScoreMap.get(mapKey);
                            keyWorkScore += assessmentScore.getKeyWorkScore();
                        }
                        assessmentScore.setKeyWorkScore(keyWorkScore);
                        assessmentScoreMap.put(mapKey, assessmentScore);
                    }
                });
            });
        }
        // 保存得分细则数据
        this.saveBatch(assessmentScoreDetailsList);

        // 返回得分Map供上层方法统一处理
        return assessmentScoreMap;
    }

    /**
     * 重新统计参加活动得分（内部方法，返回得分Map）
     *
     * @param memberId 用户ID，为null时统计所有用户
     * @return 得分Map，key为"category:memberId:year"
     */
    private Map<String, AssessmentScore> reCalculateActivityScoreInternal(Long memberId) {
        // 获取所有未删除的活动数据
        List<Activity> activities = activityMapper.selectList(new LambdaQueryWrapper<Activity>()
                .eq(Activity::getIsDeleted, 0)
                .isNotNull(Activity::getCategory)
                .ne(Activity::getCategory, "")
                .like(memberId != null, Activity::getParticipants, "%{\"id\":" + memberId +
                        "}%"));
        if (activities.isEmpty()) {
            return new HashMap<>();
        }
        // 删除所有的参加活动得分细则
        this.remove(new LambdaQueryWrapper<AssessmentScoreDetails>()
                .eq(AssessmentScoreDetails::getType, AssessmentItemTypeEnum.ACTIVITY)
                .or().eq(AssessmentScoreDetails::getType, AssessmentItemTypeEnum.CHARITY)
                .eq(memberId != null, AssessmentScoreDetails::getMemberId, memberId));

        // 需要更新插入的数据
        List<AssessmentScoreDetails> assessmentScoreDetailsList = new ArrayList<>();
        // 需要更新的总得分的数据的map
        Map<String, AssessmentScore> assessmentScoreMap = new HashMap<>();
        // 获取当前登录用户ID
        Long createBy = SecurityUtils.getUserId() == null ? 0L : SecurityUtils.getUserId();
        for (Activity activity : activities) {
            // 获取对应的活动分类列表
            List<ScoringCategoryEnum> categoryList = Arrays.stream(activity.getCategory().split(","))
                    .map(ScoringCategoryEnum::getEnumByValue).collect(Collectors.toList());
            // 获取活动参与人员id列表
            List<Long> participantIds;
            if (memberId == null) {
                participantIds = JSONUtil.toList(activity.getParticipants(), Map.class)
                        .stream()
                        .map(map -> Long.valueOf(map.get("id").toString()))
                        .collect(Collectors.toList());
            } else {
                participantIds = Arrays.asList(memberId);
            }
            // 活动对应的履职类别
            AssessmentItemTypeEnum activityType;
            if (ActivityTypeEnum.CONTRIBUTION.equals(activity.getActivityType())) {
                activityType = AssessmentItemTypeEnum.CHARITY;
            } else {
                activityType = AssessmentItemTypeEnum.ACTIVITY;
            }
            // 遍历参与人员与活动分类添加需要更新的细则对象
            participantIds.forEach(participantId -> {
                categoryList.forEach(originalCategory -> {
                    // 转换一下对应的枚举
                    AssessmentCategoryEnum category = ScoringCategoryEnum.toAssessmentCategoryEnum(originalCategory);
                    // 获取对应的计分规则
                    AssessmentRuleCacheBO rule = assessmentRulesCacheUtils.getAssessmentRuleCache(category,
                            activityType.getValue() + ":" + activity.getActivityType().getValue());
                    // 如果对应的规则存在则添加进待插入列表
                    if (rule != null) {
                        // 即将插入的新数据对象
                        AssessmentScoreDetails assessmentScoreDetails = new AssessmentScoreDetails();
                        assessmentScoreDetails.setCategory(category);
                        assessmentScoreDetails.setType(activityType);
                        assessmentScoreDetails.setMemberId(participantId);
                        assessmentScoreDetails.setDataId(activity.getId());
                        assessmentScoreDetails.setCreateBy(createBy);
                        assessmentScoreDetails.setRemark("参加活动重新统计分数");
                        assessmentScoreDetails.setRuleId(rule.getId());
                        assessmentScoreDetailsList.add(assessmentScoreDetails);

                        // 统计活动相关的总得分
                        AssessmentScore assessmentScore = new AssessmentScore();
                        String mapKey = category.getValue() + ":" + participantId + ":"
                                + activity.getStartTime().getYear();
                        assessmentScore.setCategory(category);
                        assessmentScore.setMemberId(participantId);
                        assessmentScore.setYear(activity.getStartTime().getYear());
                        Integer activityScore = rule.getScore();
                        if (assessmentScoreMap.get(mapKey) != null) {
                            assessmentScore = assessmentScoreMap.get(mapKey);
                            activityScore += assessmentScore.getActivityScore();
                        }
                        assessmentScore.setActivityScore(activityScore);
                        assessmentScoreMap.put(mapKey, assessmentScore);
                    }
                });
            });
        }
        // 保存得分细则数据
        this.saveBatch(assessmentScoreDetailsList);

        // 返回得分Map供上层方法统一处理
        return assessmentScoreMap;
    }

    /**
     * 重新统计办好事、解难题、做公益情况得分（内部方法，返回得分Map）
     *
     * @param memberId 用户ID，为null时统计所有用户
     * @return 得分Map，key为"category:memberId:year"
     */
    private Map<String, AssessmentScore> reCalculateCharityScoreInternal(Long memberId) {
        // 目前办好事、解难题、做公益情况得分逻辑为空，返回空Map
        return new HashMap<>();
    }
}
