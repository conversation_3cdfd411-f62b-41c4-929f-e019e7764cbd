<template>
  <div class="app-container">
    <h1 class="text-center">年末履职考核计分标准一览表</h1>
    <div v-if="false" class="flex justify-end">
      <el-form-item label="所属部门" class="w-220px">
        <el-select v-model="selectedAssessmentRulesCategory" placeholder="请选择所属部门">
          <el-option
            v-for="item in assessmentRulesCategoryOptions"
            :key="item.label"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
    </div>

    <SearchPage
      :service="searchPageService"
      :form-items="searchPageFormItems"
      :tableColumns="searchPageTableColumns"
      :table-show-overflow-tooltip="false"
      :table-stripe="false"
      :span-method="searchPageSpanMethod"
      tableAlign="center"
    >
      <template #pagination>
        <span></span>
      </template>
    </SearchPage>
  </div>
</template>

<script setup lang="ts">
import SearchPage from "@/components/SearchPage/index.vue";

import {
  AssessmentCategoryEnum,
  AssessmentCategoryDicts,
} from "@/views/assessment/configs/AssessmentCategoryEnum";
import { getAssessmentRulesApi } from "@/api/assessment/rules";
import { AssessmentRulesPageVO } from "@/api/assessment/types/rules";
import { RESULT_LIST } from "@/const/result";
import { FormItemsUnionTypeList } from "@/components/Form/type";
import { TableColumnType } from "@/components/Table/type";
import { SpanMethodParams } from "@/components/SearchPage/type";
import { isNotNull } from "@/utils";

/** 考核表所属部门 */
const assessmentRulesCategoryOptions = AssessmentCategoryDicts;

/** 当前选中的考核表所属部门 */
const selectedAssessmentRulesCategory = ref<AssessmentCategoryEnum>(AssessmentCategoryEnum.ZSH);

/** searchPage的service */
const searchPageService = async (params: any) => {
  return getAssessmentRulesApi({ category: selectedAssessmentRulesCategory.value, ...params }).then(
    (res) => {
      return {
        [RESULT_LIST.LIST]: res,
        [RESULT_LIST.TOTAL]: res.length,
      };
    }
  );
};

/** 搜索表单配置 */
const searchPageFormItems: FormItemsUnionTypeList = [
  // {
  //   is: "select",
  //   formItem: {
  //     label: "履职项目类别",
  //     prop: "category",
  //   },
  //   props: {
  //     options: AssessmentCategoryDicts,
  //   },
  //   width: "250px",
  // },
  // {
  //   is: "input",
  //   formItem: {
  //     label: "履职项目名称",
  //     prop: "ruleName",
  //   },
  // },
  // {
  //   is: "input",
  //   formItem: {
  //     label: "履职项目编号",
  //     prop: "ruleCode",
  //   },
  // },
];

/** 搜索表单的表格列配置 */
const searchPageTableColumns: TableColumnType<AssessmentRulesPageVO> = {
  // categoryLabel: "履职项目类别",
  // ruleCode: "履职项目编号",
  itemName: {
    type: "default",
    label: "履职项目",
    align: "center",
    emptyText: "",
    width: 350,
  },
  // itemCode: "履职规则编号",
  ruleName: {
    type: "default",
    label: "具体量化内容",
    align: "center",
    emptyText: "",
  },
  score: {
    type: "default",
    label: "记分标准",
    align: "center",
    emptyText: "",
    width: 110,
    formatter: (value: number) => {
      return (!isNotNull(value) ? "0" : value < 0 ? value : `+${value}`) + "分";
    },
  },
  // limitMax: "得分上限",
  // limitMin: "得分下限",
  // sort: "排序",s
};

/** 需要合并的列的label */
const mergeColumnLabels = ["履职项目"];

/** 表格行合并方法 */
const searchPageSpanMethod = ({
  row,
  column,
  rowIndex,
  columnIndex: _columnIndex,
  data,
}: SpanMethodParams<AssessmentRulesPageVO>) => {
  // 需要合并的列的label
  const columnLabel = column.label;
  if (mergeColumnLabels.includes(columnLabel)) {
    let rowSpan = 1;
    let isGroupFirstRow = rowIndex === 0;
    switch (columnLabel) {
      case "履职项目":
        rowSpan = row.itemSpan || 1;
        isGroupFirstRow = data[rowIndex - 1]?.itemName !== row.itemName;
        break;
    }

    if (isGroupFirstRow) {
      return {
        rowspan: rowSpan,
        colspan: 1,
      };
    }
    return {
      rowspan: 0,
      colspan: 0,
    };
  }

  return {
    rowspan: 1,
    colspan: 1,
  };
};
</script>

<style scoped lang="scss"></style>
