package com.youlai.boot.modules.assessment.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.youlai.boot.common.base.BaseEntity;
import com.youlai.boot.common.enums.AssessmentCategoryEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 履职考核得分总表实体对象
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Getter
@Setter
@TableName("tsz_assessment_score")
public class AssessmentScore extends BaseEntity {

    /**
     * 得分归类(ZSH: 总商会 XLH: 新联会)
     */
    private AssessmentCategoryEnum category;

    /**
     * 被考核人（总商会/新联会会员）用户id
     */
    private Long memberId;

    /**
     * 年度
     */
    private Integer year;

    /**
     * 活动得分
     */
    private Integer activityScore;

    /**
     * 会议得分
     */
    private Integer meetingScore;

    /**
     * 重点工作得分
     */
    private Integer keyWorkScore;

    /**
     * 营商环境问题报送得分
     */
    private Integer environmentScore;

    /**
     * 意见建议得分
     */
    private Integer opinionScore;

    /**
     * 总得分
     */
    // private Integer score;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 更新人ID
     */
    private Long updateBy;

    /**
     * 备注
     */
    private String remark;

}
