<template>
  <div class="p-15px box-size w-full">
    <SearchPage
      :service="searchPageService"
      :form-items="searchPageFormItems"
      :table-columns="searchPageTableColumns"
    ></SearchPage>

    <AssessmentDetailDialog
      v-model:visiable="visiableDetail"
      :assessment-result="currentRowData"
      :assessment-item-type="currentAssessmentItemType"
      @closed="handleClosedDetailDialog"
    ></AssessmentDetailDialog>
  </div>
</template>

<script setup lang="ts">
import SearchPage from "@/components/SearchPage/index.vue";
import AssessmentDetailDialog from "./_comp/assessmentDetailDialog.vue";
import { getAssessmentTotalScorePageApi } from "@/api/assessment/score";
import { FormItemsUnionTypeList } from "@/components/Form/type";
import { AssessmentCategoryDicts } from "@/views/assessment/configs/AssessmentCategoryEnum";
import { AssessmentScoreVO } from "@/api/assessment/types/score";
import { TableColumnType } from "@/components/Table/type";
import { AssessmentItemTypeEnum } from "@/views/assessment/configs/AssessmentItemTypeEnum";
import { ElLink } from "element-plus";

/** SearchPage 的 service */
const searchPageService = async (params: any) => {
  return getAssessmentTotalScorePageApi(params);
};

/** SerachPage 组件的 formItems */
const searchPageFormItems: FormItemsUnionTypeList = [
  {
    is: "date-picker",
    formItem: {
      label: "年度",
      prop: "year",
    },
    props: {
      type: "year",
      format: "YYYY",
      valueFormat: "YYYY",
    },
  },
  {
    is: "select",
    formItem: {
      label: "会员所属商会",
      prop: "category",
    },
    props: {
      options: AssessmentCategoryDicts,
      clearable: true,
    },
    width: 280,
  },
  {
    is: "input",
    formItem: {
      label: "会员名称",
      prop: "memberName",
    },
  },
];

/** SerachPage 组件的 tableColumns */
const searchPageTableColumns: TableColumnType<AssessmentScoreVO> = {
  index: "序号",
  year: "年度",
  memberName: "会员名称",
  categoryLabel: "会员所属商会",
  company: "所属单位",
  // activityCount: "参加活动（次）",
  activityScore: {
    label: "参加活动得分",
    elFormatter: (row: AssessmentScoreVO) => {
      return h(
        ElLink,
        {
          type: "primary",
          onClick: () => handleClickScoreCell(row, AssessmentItemTypeEnum.ACTIVITY),
        },
        row.activityScore
      );
    },
  },
  // meetingCount: "参加会议（次）",
  meetingScore: {
    label: "参加会议得分",
    elFormatter: (row: AssessmentScoreVO) => {
      return h(
        ElLink,
        {
          type: "primary",
          onClick: () => handleClickScoreCell(row, AssessmentItemTypeEnum.MEETING),
        },
        row.meetingScore
      );
    },
  },
  // keyWorkCount: "年度重点工作（次）",
  keyWorkScore: {
    label: "年度重点工作得分",
    elFormatter: (row: AssessmentScoreVO) => {
      return h(
        ElLink,
        {
          type: "primary",
          onClick: () => handleClickScoreCell(row, AssessmentItemTypeEnum.KEY_WORK),
        },
        row.keyWorkScore
      );
    },
  },
  // environmentCount: "营商环境问题报送（篇）",
  environmentScore: {
    label: "营商环境问题报送得分",
    elFormatter: (row: AssessmentScoreVO) => {
      return h(
        ElLink,
        {
          type: "primary",
          onClick: () => handleClickScoreCell(row, AssessmentItemTypeEnum.ENVIRONMENT),
        },
        row.environmentScore
      );
    },
  },
  // opinionCount: "意见建议（篇）",
  opinionScore: {
    label: "意见建议得分",
    elFormatter: (row: AssessmentScoreVO) => {
      return h(
        ElLink,
        {
          type: "primary",
          onClick: () => handleClickScoreCell(row, AssessmentItemTypeEnum.OPINION),
        },
        row.opinionScore
      );
    },
  },
  score: "总分",
};

/** 显示对应的弹框类型 */
const currentAssessmentItemType = ref(AssessmentItemTypeEnum.ACTIVITY);

/** 当前操作的行数据 */
const currentRowData = ref<AssessmentScoreVO>({});

/** 显示的得分详情弹框 */
const visiableDetail = ref(false);

/** 点击显示详情弹框 */
const handleClickScoreCell = (
  row: AssessmentScoreVO,
  assessmentItemType: AssessmentItemTypeEnum
) => {
  currentRowData.value = row;
  currentAssessmentItemType.value = assessmentItemType;
  visiableDetail.value = true;
};

/** 得分详情弹框关闭后触发 */
const handleClosedDetailDialog = () => {
  // TODO: 可以重置一下对应的显示数据，也可以不处理
};
</script>

<style scoped lang="scss"></style>
