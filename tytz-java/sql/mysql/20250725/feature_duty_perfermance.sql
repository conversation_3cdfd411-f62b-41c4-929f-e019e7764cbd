SET NAMES utf8mb4;

-- --------------------------------
-- 履职评分模块重构相关sql
-- --------------------------------

-- --------------------------------
-- 考核规则表
DROP TABLE IF EXISTS `tsz_assessment_rules`;
CREATE TABLE `tsz_assessment_rules` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `category` varchar(255) NOT NULL DEFAULT 'ZSH' COMMENT '履职项目类别（ZSH: 总商会, XLH: 新联会）',
  `rule_name` varchar(255) NOT NULL COMMENT '履职项目名称',
  `rule_code` varchar(64) NOT NULL COMMENT '履职项目编号（ACTIVITY: 活动, MEETING: 会议, KEY_WORK: 重点工作, ENVIRONMENT: 营商环境问题报送, OPINION: 意见建议, CHARITY: 公益慈善活动...）',
  `parent_id` bigint NOT NULL DEFAULT 0 COMMENT '父级规则ID',
  `tree_path` varchar(255) NOT NULL DEFAULT '0' COMMENT '父级规则路径',
  `sort` int DEFAULT NULL COMMENT '排序',
  `score` int NOT NULL DEFAULT 0 COMMENT '得分（非空，默认值为0，主分类为0，规则才有具体的分数）',
  `limit_max` int DEFAULT NULL COMMENT '得分上限（为null时无限制）',
  `limit_min` int DEFAULT NULL COMMENT '得分下限（为null时无限制）',
  `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除（0: 未删除, 1: 已删除）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_rule_code` (`category`, `rule_code`)

) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '履职考核规则表';

-- 考核规则表初始数据
-- 清空数据
DELETE FROM tsz_assessment_rules;
-- 总商会
INSERT INTO `tsz_assessment_rules` (`id`, `category`, `rule_name`, `rule_code`, `parent_id`, `tree_path`, `sort`, `score`, `limit_max`, `limit_min`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`)
values
(1, 'ZSH', '一、参加会议（以次数计）', 'MEETING', 0, '0', 1, 0, null, null, null, NOW(), null, null, 0),
(2, 'ZSH', '参加会员（代表）大会1次', 'MEETING:MEMBER', 1, '0,1', 2, 5, null, null, null, NOW(), null, null, 0),
(3, 'ZSH', '参加会长会议', 'MEETING:PRESIDENT', 1, '0,1', 3, 2, null, null, null, NOW(), null, null, 0),
(4, 'ZSH', '参加理事会会议', 'MEETING:DIRECTOR', 1, '0,1', 4, 2, null, null, null, NOW(), null, null, 0),
(5, 'ZSH', '二、参加活动（以次数计）', 'ACTIVITY', 0, '0', 5, 0, null, null, null, NOW(), null, null, 0),
(6, 'ZSH', '参加总商会/新联会组织的调研、视察、考察等活动', 'ACTIVITY:RESEARCH', 5, '0,5', 6, 1, null, null, null, NOW(), null, null, 0),
(7, 'ZSH', '受总商会/新联会委托参加市区两级组成部门等单位组织的行风评议、特约监督及营商环境等工作会议活动', 'ACTIVITY:SUPERVISION', 5, '0,5', 7, 1, null, null, null, NOW(), null, null, 0),
(8, 'ZSH', '参加市、区级部门组织的培训活动', 'ACTIVITY:TRAINING', 5, '0,5', 8, 2, null, null, null, NOW(), null, null, 0),
(9, 'ZSH', '参加与总商会/新联会工作相关的各类会议与团体活动情况', 'ACTIVITY:MEETING', 5, '0,5', 9, 1, null, null, null, NOW(), null, null, 0),
(10, 'ZSH', '三、营商环境问题报送（以每篇计）', 'ENVIRONMENT', 0, '0', 10, 0, null, null, null, NOW(), null, null, 0),
(11, 'ZSH', '提交', 'ENVIRONMENT:SUBMITTED', 10, '0,10', 11, 1, null, null, null, NOW(), null, null, 0),
(12, 'ZSH', '被采纳', 'ENVIRONMENT:ADOPTED', 10, '0,10', 12, 2, null, null, null, NOW(), null, null, 0),
(13, 'ZSH', '得到区领导批示', 'ENVIRONMENT:INSTRUCTED_DISTRICT', 10, '0,10', 13, 3, null, null, null, NOW(), null, null, 0),
(14, 'ZSH', '得到市领导批示', 'ENVIRONMENT:INSTRUCTED_CITY', 10, '0,10', 14, 4, null, null, null, NOW(), null, null, 0),
(15, 'ZSH', '四、意见征集汇总报送（以每篇计）', 'OPINION', 0, '0', 15, 0, null, null, null, NOW(), null, null, 0),
(16, 'ZSH', '提交', 'OPINION:SUBMITTED', 15, '0,15', 16, 1, null, null, null, NOW(), null, null, 0),
(17, 'ZSH', '被采纳', 'OPINION:ADOPTED', 15, '0,15', 17, 2, null, null, null, NOW(), null, null, 0),
(18, 'ZSH', '五、参加年度重点工作（综合分析1年来会员参加重点工作情况计分）', 'KEY_WORK', 0, '0', 18, 0, null, null, null, NOW(), null, null, 0),
(19, 'ZSH', '助力项目建设服务，参加引进外资活动', 'KEY_WORK:PROJECT_SERVICE', 18, '0,18', 19, 3, null, null, null, NOW(), null, null, 0),
(20, 'ZSH', '助推创一流营商环境', 'KEY_WORK:BUSINESS_ENVIRONMENT', 18, '0,18', 20, 3, null, null, null, NOW(), null, null, 0),
(21, 'ZSH', '完成总商会/新联会交办的其他任务', 'KEY_WORK:OTHER_TASKS', 18, '0,18', 21, 3, null, null, null, NOW(), null, null, 0),
(22, 'ZSH', '六、办好事、解难题、做公益情况', 'CHARITY', 0, '0', 22, 0, null, null, null, NOW(), null, null, 0),
(23, 'ZSH', '以会员身份为人民群众办好事、解难题、做公益慈善等贡献（以次数计）', 'CHARITY:CONTRIBUTION', 22, '0,22', 23, 3, null, null, null, NOW(), null, null, 0);

-- 新联会
INSERT INTO `tsz_assessment_rules` (`id`, `category`, `rule_name`, `rule_code`, `parent_id`, `tree_path`, `sort`, `score`, `limit_max`, `limit_min`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`)
values
(101, 'XLH', '一、参加会议（以次数计）', 'MEETING', 0, '0', 1, 0, null, null, null, NOW(), null, null, 0),
(102, 'XLH', '参加会员（代表）大会1次', 'MEETING:MEMBER', 101, '0,101', 2, 5, null, null, null, NOW(), null, null, 0),
(103, 'XLH', '参加会长会议', 'MEETING:PRESIDENT', 101, '0,101', 3, 2, null, null, null, NOW(), null, null, 0),
(104, 'XLH', '参加理事会会议', 'MEETING:DIRECTOR', 101, '0,101', 4, 2, null, null, null, NOW(), null, null, 0),
(105, 'XLH', '二、参加活动（以次数计）', 'ACTIVITY', 0, '0', 5, 0, null, null, null, NOW(), null, null, 0),
(106, 'XLH', '参加总商会/新联会组织的调研、视察、考察等活动', 'ACTIVITY:RESEARCH', 105, '0,105', 6, 1, null, null, null, NOW(), null, null, 0),
(107, 'XLH', '受总商会/新联会委托参加市区两级组成部门等单位组织的行风评议、特约监督及营商环境等工作会议活动', 'ACTIVITY:SUPERVISION', 105, '0,105', 7, 1, null, null, null, NOW(), null, null, 0),
(108, 'XLH', '参加市、区级部门组织的培训活动', 'ACTIVITY:TRAINING', 105, '0,105', 8, 2, null, null, null, NOW(), null, null, 0),
(109, 'XLH', '参加与总商会/新联会工作相关的各类会议与团体活动情况', 'ACTIVITY:MEETING', 105, '0,105', 9, 1, null, null, null, NOW(), null, null, 0),
(110, 'XLH', '三、营商环境问题报送（以每篇计）', 'ENVIRONMENT', 0, '0', 10, 0, null, null, null, NOW(), null, null, 0),
(111, 'XLH', '提交', 'ENVIRONMENT:SUBMITTED', 110, '0,110', 11, 1, null, null, null, NOW(), null, null, 0),
(112, 'XLH', '被采纳', 'ENVIRONMENT:ADOPTED', 110, '0,110', 12, 2, null, null, null, NOW(), null, null, 0),
(113, 'XLH', '得到区领导批示', 'ENVIRONMENT:INSTRUCTED_DISTRICT', 110, '0,110', 13, 3, null, null, null, NOW(), null, null, 0),
(114, 'XLH', '得到市领导批示', 'ENVIRONMENT:INSTRUCTED_CITY', 110, '0,110', 14, 4, null, null, null, NOW(), null, null, 0),
(115, 'XLH', '四、意见征集汇总报送（以每篇计）', 'OPINION', 0, '0', 15, 0, null, null, null, NOW(), null, null, 0),
(116, 'XLH', '提交', 'OPINION:SUBMITTED', 115, '0,115', 16, 1, null, null, null, NOW(), null, null, 0),
(117, 'XLH', '被采纳', 'OPINION:ADOPTED', 115, '0,115', 17, 2, null, null, null, NOW(), null, null, 0),
(118, 'XLH', '五、参加年度重点工作（综合分析1年来会员参加重点工作情况计分）', 'KEY_WORK', 0, '0', 18, 0, null, null, null, NOW(), null, null, 0),
(119, 'XLH', '助力项目建设服务，参加引进外资活动', 'KEY_WORK:PROJECT_SERVICE', 118, '0,118', 19, 3, null, null, null, NOW(), null, null, 0),
(120, 'XLH', '助推创一流营商环境', 'KEY_WORK:BUSINESS_ENVIRONMENT', 118, '0,118', 20, 3, null, null, null, NOW(), null, null, 0),
(121, 'XLH', '完成总商会/新联会交办的其他任务', 'KEY_WORK:OTHER_TASKS', 118, '0,118', 21, 3, null, null, null, NOW(), null, null, 0),
(122, 'XLH', '六、办好事、解难题、做公益情况', 'CHARITY', 0, '0', 22, 0, null, null, null, NOW(), null, null, 0),
(123, 'XLH', '以会员身份为人民群众办好事、解难题、做公益慈善等贡献（以次数计）', 'CHARITY:CONTRIBUTION', 122, '0,122', 23, 3, null, null, null, NOW(), null, null, 0);


-- --------------------------------


-- --------------------------------
-- 履职管理新菜单
DELETE FROM sys_menu WHERE id IN(253, 254, 255) ;
INSERT INTO sys_menu (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `create_time`, `update_time`, `params`) VALUES (253, 0, '0', '履职评分', 2, 'Assessment', '/assessment', 'Layout', NULL, 0, 1, 1, 6, 'menu', '/assessment/assessment-result', NOW(), NOW(), NULL);
INSERT INTO sys_menu (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `create_time`, `update_time`, `params`) VALUES (254, 253, '0,253', '评分规则', 1, 'AssessmentRules', 'assessment-rules', 'assessment/assessment_rules/index', NULL, 0, 1, 1, 1, 'code', NULL, NOW(), NOW(), NULL);
INSERT INTO sys_menu (`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `create_time`, `update_time`, `params`) VALUES (255, 253, '0,253', '评级表管理', 1, 'AssessmentResult', 'assessment-result', 'assessment/assessment_result/index', NULL, 0, 1, 1, 2, 'role', NULL, NOW(), NOW(), NULL);

-- 考核得分细则表
DROP TABLE IF EXISTS `tsz_assessment_score_details`;
CREATE TABLE `tsz_assessment_score_details` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `category` varchar(255) NOT NULL COMMENT '得分归类(ZSH: 总商会 XLH: 新联会)',
  `type` varchar(32) NOT NULL COMMENT '得分类型（MEETING: 会议, ACTIVITY: 活动, KEY_WORK: 重点工作, ENVIRONMENT: 营商环境问题报送, OPINION: 意见建议, CHARITY: 公益慈善活动...）',
  `member_id` bigint NOT NULL COMMENT '被考核人（总商会/新联会会员）用户id',
  `rule_id` bigint NOT NULL COMMENT '对应的考核规则id',
  `data_id` bigint NOT NULL COMMENT '得分对应的数据id（会议id、活动id、重点工作id等）',
  `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(128) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_score_detail` (`category`, `type`, `member_id`, `rule_id`, `data_id`)

) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '履职考核得分细则表';

-- 根据旧有数据初始化数据


-- --------------------------------


-- --------------------------------
-- 考核得分总表
DROP TABLE IF EXISTS `tsz_assessment_score`;
CREATE TABLE `tsz_assessment_score` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `category` varchar(255) NOT NULL COMMENT '得分归类(ZSH: 总商会 XLH: 新联会)',
  `member_id` bigint NOT NULL COMMENT '被考核人（总商会/新联会会员）用户id',
  `year` int NOT NULL COMMENT '年度',
  `activity_score` int NOT NULL DEFAULT 0 COMMENT '活动得分',
  `meeting_score` int NOT NULL DEFAULT 0 COMMENT '会议得分',
  `key_work_score` int NOT NULL DEFAULT 0 COMMENT '重点工作得分',
  `environment_score` int NOT NULL DEFAULT 0 COMMENT '营商环境问题报送得分',
  `opinion_score` int NOT NULL DEFAULT 0 COMMENT '意见建议得分',
  -- 冗余字段去掉
  -- `score` int GENERATED ALWAYS AS (
  --   `activity_score` + `meeting_score` + `key_work_score` + `environment_score` + `opinion_score`
  -- ) STORED COMMENT '总得分',
  `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(128) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_score` (`category`, `member_id`, `year`)

) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '履职考核得分总表';



-- --------------------------------
-- 营商环境问题表修改（添加status字段）
ALTER TABLE `tsz_problem` 
ADD COLUMN `status` varchar(50) NOT NULL DEFAULT 'SUBMITTED' COMMENT '问题状态（SUBMITTED： 已提交, ADOPTED： 已采纳, REJECTED： 不予采纳, INSTRUCTED_DISTRICT： 得到区领导批示, INSTRUCTED_CITY： 得到市领导批示）' AFTER `category`;
-- 营商环境问题表修改后初始化数据
-- 先默认全部是提交
UPDATE tsz_problem SET status = 'SUBMITTED';
-- 更新未采纳状态数据
UPDATE tsz_problem SET status = 'REJECTED' WHERE adopt_status = 'REJECT';
-- 更新采纳状态
UPDATE tsz_problem SET status = 'ADOPTED' WHERE adopt_status = 'PASS';
-- 更新批示状态
UPDATE tsz_problem SET status = 'INSTRUCTED_DISTRICT' WHERE instruction_status = 'PASS' AND leader_instruction = 'Q';
UPDATE tsz_problem SET status = 'INSTRUCTED_CITY' WHERE instruction_status = 'PASS' AND leader_instruction = 'S';

COMMIT;