<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youlai.boot.modules.assessment.mapper.AssessmentScoreMapper">
  <!-- 获取履职考核得分总表分页数据 -->
  <select id="getAssessmentScorePage" resultType="com.youlai.boot.modules.assessment.model.vo.AssessmentScoreVO">
        SELECT
	        su.id AS member_id,
	        su.nickname AS member_name,
	        su.company,
	        su.dept_code AS category,
    <!-- COALESCE(s.category, su.dept_code) AS category, -->
	        COALESCE(s.YEAR, #{queryParams.year}, YEAR(NOW())) AS YEAR,
	        COALESCE(s.activity_score, 0) AS activity_score,
	        COALESCE(s.meeting_score, 0) AS meeting_score,
	        COALESCE(s.key_work_score, 0) AS key_work_score,
	        COALESCE(s.environment_score, 0) AS environment_score,
	        COALESCE(s.opinion_score, 0) AS opinion_score,
            COALESCE(
                (COALESCE(s.activity_score, 0) + 
                 COALESCE(s.meeting_score, 0) + 
                 COALESCE(s.key_work_score, 0) + 
                 COALESCE(s.environment_score, 0) + 
                 COALESCE(s.opinion_score, 0)), 
                0) AS score,,
            s.remark,
            s.create_time,
            s.update_time,
            -- 统计各类型的次数
            COALESCE(activity_count.count, 0) AS activity_count,
            COALESCE(meeting_count.count, 0) AS meeting_count,
            COALESCE(key_work_count.count, 0) AS key_work_count,
            COALESCE(environment_count.count, 0) AS environment_count,
            COALESCE(opinion_count.count, 0) AS opinion_count
        FROM
            (SELECT 
                u.id,
                u.username,
                u.nickname,
                u.mobile,
                u.status,
                u.company,
                -- 根据部门类型返回对应的部门编号
                CASE 
                    WHEN d.dept_type = 'POSITION' THEN 
                        (SELECT parent_dept.code FROM sys_dept parent_dept WHERE parent_dept.id = d.parent_id)
                    ELSE d.code
                END AS dept_code
            FROM sys_user u
            INNER JOIN sys_user_dept ud ON u.id = ud.user_id
            INNER JOIN sys_dept d ON ud.dept_id = d.id
            WHERE 
                -- 查找ZSH或XLH部门及其所有子部门
                d.tree_path LIKE '%,30%' OR d.id = 30  -- ZSH及其子部门
                OR d.tree_path LIKE '%,36%' OR d.id = 36  -- XLH及其子部门
                AND u.is_deleted = 0  -- 只查询未删除的用户
                AND d.is_deleted = 0  -- 只查询未删除的部门
            ORDER BY 
                dept_code, u.id) su
        LEFT JOIN tsz_assessment_score s ON su.id = s.member_id AND su.dept_code = s.category
    <!-- LEFT JOIN sys_user u ON s.member_id = u.id -->
        -- 统计活动次数
        LEFT JOIN (
            SELECT
                tasd.category,
            	tasd.member_id,
            	year,
            	COUNT(*) AS count
            FROM
            	(SELECT DISTINCT 
                    t.category, t.member_id, t.type, t.data_id, YEAR(tat.start_time) AS year
                 FROM tsz_assessment_score_details t
                 LEFT JOIN tsz_activity tat ON tat.id = t.data_id
                 WHERE tat.is_deleted = 0 AND t.type = 'ACTIVITY'
    <if test="queryParams.category != null">
            	AND tasd.category = #{queryParams.category.value}
        </if>
    <if test="queryParams.year != null and queryParams.year != ''">
                AND tat.start_time &gt;= CONCAT(#{queryParams.year}, '-01-01 00:00:00') 
        	    AND tat.start_time &lt;= CONCAT(#{queryParams.year}, '-12-31 23:59:59')
        </if>
                ) tasd
	        GROUP BY tasd.category, tasd.member_id, tasd.type, year
        ) activity_count ON s.member_id = activity_count.member_id AND s.year = activity_count.year AND s.category = activity_count.category 
        -- 统计会议次数
        LEFT JOIN (
            SELECT
                tasd.category,
            	tasd.member_id,
            	year,
            	COUNT(*) AS count
            FROM
            	(SELECT DISTINCT 
                    t.category, t.member_id, t.type, t.data_id, YEAR(tm.start_time) AS year
                 FROM tsz_assessment_score_details t
                 LEFT JOIN tsz_meeting tm ON tm.id = t.data_id
                 WHERE tm.is_deleted = 0 AND t.type = 'MEETING'
    <if test="queryParams.category != null">
            	AND tasd.category = #{queryParams.category.value}
        </if>
    <if test="queryParams.year != null and queryParams.year != ''">
                AND tm.start_time &gt;= CONCAT(#{queryParams.year}, '-01-01 00:00:00') 
        	    AND tm.start_time &lt;= CONCAT(#{queryParams.year}, '-12-31 23:59:59')
        </if>
                ) tasd
            GROUP BY tasd.category, tasd.member_id, tasd.type, year
        ) meeting_count ON s.member_id = meeting_count.member_id AND s.year = meeting_count.year AND s.category = meeting_count.category 
        -- 统计重点工作次数
        LEFT JOIN (
            SELECT
                tasd.category,
            	tasd.member_id,
            	year,
            	COUNT(*) AS count
            FROM
            	(SELECT DISTINCT 
                    t.category, t.member_id, t.type, t.data_id, YEAR(tkw.start_time) AS year
                 FROM tsz_assessment_score_details t
                 LEFT JOIN tsz_key_work tkw ON tkw.id = t.data_id
                 WHERE tkw.is_deleted = 0 AND t.type = 'KEY_WORK'
    <if test="queryParams.category != null">
            	AND tasd.category = #{queryParams.category.value}
        </if>
    <if test="queryParams.year != null and queryParams.year != ''">
  	            AND tkw.start_time &gt;= CONCAT(#{queryParams.year}, '-01-01 00:00:00')
        	    AND tkw.start_time &lt;= CONCAT(#{queryParams.year}, '-12-31 23:59:59')
        </if>
                ) tasd
            GROUP BY tasd.category, tasd.member_id, tasd.type, year
        ) key_work_count ON s.member_id = key_work_count.member_id AND s.year = key_work_count.year AND s.category = key_work_count.category 
        -- 统计营商环境问题报送数量
        LEFT JOIN (
            SELECT
                tasd.category,
            	tasd.member_id,
            	year,
            	COUNT(*) AS count
            FROM
            	(SELECT DISTINCT 
                    t.category, t.member_id, t.type, t.data_id, YEAR(tb.submit_time) AS YEAR
                 FROM tsz_assessment_score_details t
                 LEFT JOIN tsz_problem tb ON tb.id = t.data_id
                 WHERE tb.is_deleted = 0 AND t.type = 'ENVIRONMENT'
    <if test="queryParams.category != null">
            	AND tasd.category = #{queryParams.category.value}
        </if>
    <if test="queryParams.year != null and queryParams.year != ''">
  	            AND tb.submit_time &gt;= CONCAT(#{queryParams.year}, '-01-01 00:00:00')
        	    AND tb.submit_time &lt;= CONCAT(#{queryParams.year}, '-12-31 23:59:59')
        </if>
                ) tasd
            GROUP BY tasd.category, tasd.member_id, tasd.type, year
        ) environment_count ON s.member_id = environment_count.member_id AND s.year = environment_count.year AND s.category = environment_count.category 
        -- 统计意见建议数量
        LEFT JOIN (
            SELECT
                tasd.category,
            	tasd.member_id,
            	year,
            	COUNT(*) AS count
            FROM
            	(SELECT DISTINCT 
                    t.category, t.member_id, t.type, t.data_id, YEAR(tot.create_time) AS YEAR
                 FROM tsz_assessment_score_details t
                 LEFT JOIN tsz_opinion tot ON tot.id = t.data_id
                 WHERE tot.is_deleted = 0 AND t.type = 'OPINION'
    <if test="queryParams.category != null">
            	AND tasd.category = #{queryParams.category.value}
        </if>
    <if test="queryParams.year != null and queryParams.year != ''">
  	            AND tot.create_time &gt;= CONCAT(#{queryParams.year}, '-01-01 00:00:00')
        	    AND tot.create_time &lt;= CONCAT(#{queryParams.year}, '-12-31 23:59:59')
        </if>
                ) tasd
            GROUP BY tasd.category, tasd.member_id, tasd.type, year
        ) opinion_count ON s.member_id = opinion_count.member_id AND s.year = opinion_count.year AND s.category = opinion_count.category 
        WHERE 1 = 1
    <if test="queryParams.category != null">
            AND s.category = #{queryParams.category.value}
        </if>
    <if test="queryParams.memberId != null">
            AND s.member_id = #{queryParams.memberId}
        </if>
    <if test="queryParams.memberName != null and queryParams.memberName != ''">
            AND su.nickname LIKE CONCAT('%', #{queryParams.memberName}, '%')
        </if>
    <if test="queryParams.year != null">
            AND s.year = #{queryParams.year}
        </if>
        GROUP BY 
                s.member_id,
            	su.id,
            	su.dept_code,
            	su.nickname,
            	su.company,
            	year,
            	s.activity_score,
            	s.meeting_score,
            	s.key_work_score,
            	s.environment_score,
            	s.opinion_score,
            	s.remark,
            	s.create_time,
            	s.update_time,
            	activity_count.count,
            	meeting_count.count,
            	key_work_count.count,
            	environment_count.count,
            	opinion_count.count 
        ORDER BY 
                score DESC, year DESC, s.create_time DESC
  </select>
  <select id="getAssessmentScorePageV2" resultType="com.youlai.boot.modules.assessment.model.vo.AssessmentScoreVO">
        SELECT
	        su.id AS member_id,
	        su.nickname AS member_name,
	        su.company,
	        su.dept_code AS category,
    <!-- COALESCE(s.category, su.dept_code) AS category, -->
	        COALESCE(s.YEAR, #{queryParams.year}, YEAR(NOW())) AS YEAR,
	        COALESCE(s.activity_score, 0) AS activity_score,
	        COALESCE(s.meeting_score, 0) AS meeting_score,
	        COALESCE(s.key_work_score, 0) AS key_work_score,
	        COALESCE(s.environment_score, 0) AS environment_score,
	        COALESCE(s.opinion_score, 0) AS opinion_score,
            COALESCE(
                (COALESCE(s.activity_score, 0) + 
                 COALESCE(s.meeting_score, 0) + 
                 COALESCE(s.key_work_score, 0) + 
                 COALESCE(s.environment_score, 0) + 
                 COALESCE(s.opinion_score, 0)), 
                0) AS score 
        FROM
            (SELECT 
                u.id,
                u.username,
                u.nickname,
                u.mobile,
                u.status,
                u.company,
                -- 根据部门类型返回对应的部门编号
                CASE 
                    WHEN d.dept_type = 'POSITION' THEN 
                        (SELECT parent_dept.code FROM sys_dept parent_dept WHERE parent_dept.id = d.parent_id)
                    ELSE d.code
                END AS dept_code
            FROM sys_user u
            INNER JOIN sys_user_dept ud ON u.id = ud.user_id
            INNER JOIN sys_dept d ON ud.dept_id = d.id
            WHERE 
                -- 查找ZSH或XLH部门及其所有子部门
                d.tree_path LIKE '%,30%' OR d.id = 30  -- ZSH及其子部门
                OR d.tree_path LIKE '%,36%' OR d.id = 36  -- XLH及其子部门
                AND u.is_deleted = 0  -- 只查询未删除的用户
                AND d.is_deleted = 0  -- 只查询未删除的部门
            ORDER BY 
                dept_code, u.id) su
        LEFT JOIN (SELECT 
                    tas.* 
                  FROM tsz_assessment_score tas 
                  WHERE 
                    1 = 1
    <if test="queryParams.category != null">
                    AND tas.category = #{queryParams.category.value}
        </if>
    <if test="queryParams.year != null">
                    AND tas.year = #{queryParams.year}
        </if>
                ) s ON su.id = s.member_id AND su.dept_code = s.category
        WHERE 1 = 1
    <if test="queryParams.memberId != null">
            AND su.id = #{queryParams.memberId}
        </if>
    <if test="queryParams.memberName != null and queryParams.memberName != ''">
            AND su.nickname LIKE CONCAT('%', #{queryParams.memberName}, '%')
        </if>
    <if test="queryParams.category != null">
            AND su.dept_code = #{queryParams.category.value}
        </if>
        GROUP BY 
                s.member_id,
            	su.id,
            	su.dept_code,
            	su.nickname,
            	su.company,
            	year,
            	s.activity_score,
            	s.meeting_score,
            	s.key_work_score,
            	s.environment_score,
            	s.opinion_score
        ORDER BY 
                score DESC, year DESC, su.id ASC
  </select>
</mapper>